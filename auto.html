<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM 智能输入法</title>
    <style>
        :root {
            --bg-color: #1a1a1e;
            --text-color: #e0e0e0;
            --primary-color: #4d90fe;
            --secondary-color: #333;
            --border-color: #444;
            --ghost-text-color: rgba(224, 224, 224, 0.4);
            --panel-bg: #25252a;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: var(--font-family);
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            max-width: 900px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 0;
        }

        .settings-panel {
            background-color: var(--panel-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 15px;
            transition: all 0.3s ease;
        }

        .settings-header {
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .settings-header::after {
            content: '▲';
            transition: transform 0.3s ease;
        }

        .settings-panel.collapsed .settings-header::after {
            transform: rotate(180deg);
        }

        .settings-content {
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
            margin-top: 10px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .settings-panel.collapsed .settings-content {
            display: none;
        }

        .setting {
            display: flex;
            flex-direction: column;
        }

        .setting label {
            margin-bottom: 8px;
            font-weight: 500;
        }

        .setting input[type="range"], .setting select {
            width: 100%;
            background-color: var(--secondary-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 4px;
            padding: 5px;
        }
        
        .setting input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
        }
        
        #prediction-count-value, #memory-size-value {
            margin-left: 10px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .editor-container {
            position: relative;
            width: 100%;
        }

        #main-editor, #ghost-text {
            width: 100%;
            min-height: 400px;
            padding: 15px;
            box-sizing: border-box;
            font-size: 16px;
            line-height: 1.8;
            border-radius: 8px;
            font-family: inherit;
        }

        #main-editor {
            position: relative;
            z-index: 1;
            background-color: transparent;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            resize: vertical;
        }

        #main-editor:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 5px var(--primary-color);
        }

        #ghost-text {
            position: absolute;
            top: 0;
            left: 0;
            color: var(--ghost-text-color);
            background-color: transparent;
            border: 1px solid transparent;
            z-index: 0;
            pointer-events: none;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        #prediction-candidates {
            list-style: none;
            padding: 0;
            margin: -10px 0 0 15px;
            background-color: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            display: flex;
            gap: 10px;
            padding: 8px 12px;
            position: absolute;
            z-index: 10;
        }

        #prediction-candidates li {
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        #prediction-candidates li.active {
            background-color: var(--primary-color);
            color: var(--bg-color);
        }
        
        #prediction-candidates li span {
            margin-right: 4px;
            color: var(--primary-color);
        }

        .status-panel {
            background-color: var(--panel-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-panel h3 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 5px;
        }
        
        #permanent-memory-display {
            white-space: pre-wrap;
            word-wrap: break-word;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>LLM 智能输入法</h1>
        <p style="text-align:center; margin-top: 5px; color: #aaa;">以 GLM API 为核心的下一代输入预测工具</p>

        <div class="settings-panel" id="settings-panel">
            <div class="settings-header" id="settings-header">
                设置
            </div>
            <div class="settings-content">
                <div class="setting">
                    <label for="prediction-count">拼音候选项数量: <span id="prediction-count-value">5</span></label>
                    <input type="range" id="prediction-count" min="1" max="20" value="5">
                </div>
                <div class="setting">
                    <label for="memory-size">记忆大小 (字符): <span id="memory-size-value">1000</span></label>
                    <input type="range" id="memory-size" min="200" max="4000" step="100" value="1000">
                </div>
                <div class="setting">
                    <label class="setting-row">
                        <input type="checkbox" id="adaptive-memory-toggle" checked>
                        启用自适应记忆 (按段落总结)
                    </label>
                </div>
                <div class="setting">
                    <label for="char-word-model">字/词模型:</label>
                    <select id="char-word-model">
                        <option value="glm-4-flash-250414" selected>GLM-4-Flash</option>
                        <option value="glm-z1-airx">GLM-Z1-Airx</option>
                    </select>
                </div>
                <div class="setting">
                    <label for="sentence-model">句子模型:</label>
                    <select id="sentence-model">
                        <option value="glm-4-flash-250414">GLM-4-Flash</option>
                        <option value="glm-z1-airx" selected>GLM-Z1-Airx</option>
                    </select>
                </div>
                <div class="setting">
                    <label for="api-key">API密钥:</label>
                    <input type="password" id="api-key" placeholder="请输入您的GLM API密钥" style="width: 100%; padding: 8px; background-color: var(--secondary-color); border: 1px solid var(--border-color); color: var(--text-color); border-radius: 4px;">
                </div>
            </div>
        </div>

        <div class="editor-container">
            <div id="ghost-text"></div>
            <textarea id="main-editor" placeholder="在此输入... 输入拼音（如 'nihao'）或中文以开始预测。按 Tab 键接受灰色建议。"></textarea>
            <ul id="prediction-candidates" style="display: none;"></ul>
        </div>

        <div class="status-panel">
            <h3>长期记忆核心</h3>
            <div id="permanent-memory-display">尚未形成记忆...</div>
        </div>

    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {

        const DEFAULT_API_KEY = "15a9ef99479944caa9ce66f0a0dfcc38.29Np5HcVS7bvgh7I";
        const API_BASE_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions";

        const dom = {
            editor: document.getElementById('main-editor'),
            ghostText: document.getElementById('ghost-text'),
            candidates: document.getElementById('prediction-candidates'),
            settings: {
                panel: document.getElementById('settings-panel'),
                header: document.getElementById('settings-header'),
                predictionCount: document.getElementById('prediction-count'),
                predictionCountValue: document.getElementById('prediction-count-value'),
                memorySize: document.getElementById('memory-size'),
                memorySizeValue: document.getElementById('memory-size-value'),
                adaptiveMemoryToggle: document.getElementById('adaptive-memory-toggle'),
                charWordModel: document.getElementById('char-word-model'),
                sentenceModel: document.getElementById('sentence-model'),
                apiKey: document.getElementById('api-key'),
            },
            status: {
                permanentMemoryDisplay: document.getElementById('permanent-memory-display'),
            }
        };

        let state = {
            settings: {
                predictionCount: parseInt(dom.settings.predictionCount.value, 10),
                memorySize: parseInt(dom.settings.memorySize.value, 10),
                adaptiveMemory: dom.settings.adaptiveMemoryToggle.checked,
                charWordModel: dom.settings.charWordModel.value,
                sentenceModel: dom.settings.sentenceModel.value,
                apiKey: dom.settings.apiKey.value || DEFAULT_API_KEY,
            },
            pinyin: {
                buffer: '',
                isActive: false,
                candidates: [],
                activeIndex: 0,
            },
            context: {
                ghostPrediction: '',
                lastCheckedText: '',
                isPredicting: false,
            },
            memory: {
                permanent: {
                    summary: '无',
                    elements: [],
                    keywords: new Set(),
                    patterns: new Set(),
                    style: '未知',
                },
                lastDistilledLength: 0,
            },
            debounceTimer: null,
        };

        async function callZhipuAPI(model, messages, temperature = 0.5) {
            const apiKey = state.settings.apiKey || DEFAULT_API_KEY;
            if (!apiKey) {
                throw new Error('API密钥未设置');
            }

            try {
                const response = await fetch(API_BASE_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: messages,
                        temperature: temperature,
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API Error (${response.status}): ${errorData.error.message}`);
                }

                const data = await response.json();
                let content = data.choices[0].message.content;
                
                if (content.includes('<think>')) {
                    content = content.split('</think>')[1] || '';
                }
                
                return content.trim();

            } catch (error) {
                console.error("API Call Failed:", error);
                dom.ghostText.textContent = `// API Error: ${error.message}`;
                setTimeout(() => { dom.ghostText.textContent = ''; }, 3000);
                return null;
            }
        }
        
        const Prompts = {
            pinyinToWords: (pinyin, count, context) => `你是一个先进的中文输入法引擎。基于以下上下文"${context}"，将拼音 '${pinyin}' 转换为 ${count} 个最合适的中文词语（考虑上下文连贯性，包括可能的符号整合）。请用单个空格分隔每个词语，不要添加序号、标点或任何其他解释。`,
            contextualPrediction: (context, permanentMemory) => {
                const memoryContext = `
# 长期记忆 (背景知识):
- 核心概述: ${permanentMemory.summary}
- 语言风格: ${permanentMemory.style}
- 关键词: ${Array.from(permanentMemory.keywords).join(', ') || '无'}
- 特征编号: ${Array.from(permanentMemory.patterns).join(', ') || '无'}
                `.trim();

                return [
                    { role: 'system', content: `你是一个智能文本预测代理。根据上下文和记忆，预测下一个最合适的元素：可以是单个字符、词语、短语、标点符号或短句。确保预测自然连贯、实用，并关注符号的使用。输出仅为预测文本本身，无任何解释或包装。` },
                    { role: 'user', content: `${memoryContext}\n\n# 当前上下文 (到光标处结束):\n...\n${context}` }
                ];
            },
            sentencePrediction: (context, permanentMemory) => {
                const memoryContext = Prompts.contextualPrediction(context, permanentMemory)[0].content; 
                return [
                    { role: 'system', content: `你是一个智能写作代理。根据上下文和记忆，续写一个完整的句子、段落或适当长度的文本，包括必要的标点符号。预测应富有逻辑、上下文相关，并考虑符号细节。输出直接为续写内容，无前缀或解释。` },
                    { role: 'user', content: `${Prompts.contextualPrediction(context, permanentMemory)[1].content}` }
                ];
            },
            memoryDistillation: (text) => {
                 return [
                    { role: 'system', content: `你是一个专业的文本分析师。请仔细阅读以下文本，并严格按照指定的JSON格式返回关键信息，不要添加任何额外的解释或说明。` },
                    { role: 'user', content: `
# 待分析文本:
"""
${text}
"""

# 提取指令:
请按照以下JSON格式返回结果：
1.  \`summary\`: 对文本内容进行不超过50字的精炼概述。
2.  \`title\`: 为这段文本起一个简洁的小标题。
3.  \`keywords\`: 列出文本中出现的专业名词、术语或人名地名 (数组格式)。
4.  \`patterns\`: 识别并列出文本中出现的特殊编号、代码或格式 (数组格式)。
5.  \`style\`: 用简短的词语描述这段文本的语言风格 (例如：正式、口语化、技术性)。

# 输出格式 (必须是严格的JSON):
{
  "summary": "...",
  "title": "...",
  "keywords": ["...", "..."],
  "patterns": ["...", "..."],
  "style": "..."
}`
                    }
                ];
            },
            combineElements: (combinedSummaries) => [
                { role: 'system', content: `你是一个记忆整合代理。将以下多个元素摘要求同存异地合并成一个 cohesive 的新元素，按照相同的JSON格式输出，包括更新后的keywords、patterns和style（聚焦共通点）。` },
                { role: 'user', content: `合并以下摘要：\n${combinedSummaries}` }
            ]
        };

        function getCursorContext() {
            const start = dom.editor.selectionStart;
            const text = dom.editor.value;
            return {
                before: text.substring(0, start),
                after: text.substring(start),
                start
            };
        }

        function handleInput(event) {
            const { before, after } = getCursorContext();
            state.context.ghostPrediction = '';
            updateGhostText(before, after);

            const pinyinMatch = before.match(/([a-z']+)$/);
            if (pinyinMatch && before.length + after.length > state.context.lastCheckedText.length) {
                state.pinyin.isActive = true;
                state.pinyin.buffer = pinyinMatch[1];
                debouncedFetchPinyinCandidates();
            } else {
                if (state.pinyin.isActive) {
                    resetPinyinState();
                }
                debouncedFetchContextualPrediction();
            }
            state.context.lastCheckedText = before + after;
            handleMemoryManagement(before + after);
        }
        
        function handleKeyDown(event) {
            if (event.key === 'Tab' && state.context.ghostPrediction) {
                event.preventDefault();
                acceptGhostPrediction();
            }
            else if (event.key === ' ') {
                if (state.pinyin.isActive) {
                    event.preventDefault();
                    selectPinyinCandidate(state.pinyin.activeIndex);
                } else {
                    resetGhostPrediction();
                }
            } 
            else if (state.pinyin.isActive && /^[1-9]$/.test(event.key)) {
                event.preventDefault();
                selectPinyinCandidate(parseInt(event.key, 10) - 1);
            }
            else if (state.pinyin.isActive && (event.key === 'ArrowUp' || event.key === 'ArrowDown')) {
                event.preventDefault();
                navigatePinyinCandidates(event.key === 'ArrowDown' ? 1 : -1);
            }
        }
        
        function acceptGhostPrediction() {
            if (!state.context.ghostPrediction) return;
            const { before, after, start } = getCursorContext();
            const newText = before + state.context.ghostPrediction + after;
            dom.editor.value = newText;
            dom.editor.selectionStart = dom.editor.selectionEnd = start + state.context.ghostPrediction.length;
            resetGhostPrediction();
            debouncedFetchContextualPrediction();
        }

        function resetGhostPrediction() {
            state.context.ghostPrediction = '';
            const { before, after } = getCursorContext();
            updateGhostText(before, after);
        }

        async function fetchPinyinCandidates() {
            if (!state.pinyin.buffer || state.context.isPredicting) return;
            
            state.context.isPredicting = true;
            const { before } = getCursorContext();
            const prePinyinContext = before.slice(0, -state.pinyin.buffer.length).slice(-100);
            const prompt = Prompts.pinyinToWords(state.pinyin.buffer, state.settings.predictionCount, prePinyinContext);
            const result = await callZhipuAPI(state.settings.charWordModel, [{role: 'user', content: prompt }], 0.2);
            state.context.isPredicting = false;
            
            if (result) {
                state.pinyin.candidates = result.split(' ').filter(Boolean);
                state.pinyin.activeIndex = 0;
                updatePinyinCandidatesUI();
            }
        }

        function selectPinyinCandidate(index) {
            const candidate = state.pinyin.candidates[index];
            if (!candidate) return;

            const { before, after, start } = getCursorContext();
            const newBefore = before.substring(0, before.length - state.pinyin.buffer.length) + candidate;
            dom.editor.value = newBefore + after;
            
            dom.editor.focus();
            dom.editor.selectionStart = dom.editor.selectionEnd = newBefore.length;

            resetPinyinState();
            
            clearTimeout(state.debounceTimer);
            fetchContextualPrediction();
        }

        function resetPinyinState() {
            state.pinyin.isActive = false;
            state.pinyin.buffer = '';
            state.pinyin.candidates = [];
            state.pinyin.activeIndex = 0;
            updatePinyinCandidatesUI();
        }
        
        async function fetchContextualPrediction() {
            const { before, after } = getCursorContext();
            if (before.length < 1 || state.context.isPredicting || state.pinyin.isActive) return;

            state.context.isPredicting = true;
            
            const context = before.slice(-500);
            const isSentenceLevel = before.length > 50 && /[。！？]$/.test(before.trim());

            const model = isSentenceLevel ? state.settings.sentenceModel : state.settings.charWordModel;
            const messages = isSentenceLevel 
                ? Prompts.sentencePrediction(context, state.memory.permanent)
                : Prompts.contextualPrediction(context, state.memory.permanent);
            
            const prediction = await callZhipuAPI(model, messages, 0.4);
            
            state.context.isPredicting = false;
            if (prediction) {
                state.context.ghostPrediction = prediction;
                updateGhostText(before, after);
            }
        }
        
        function handleMemoryManagement(fullText) {
            const length = fullText.length;

            if (length < state.settings.memorySize) return;

            let shouldDistill = false;
            let textToDistill = '';

            if (state.settings.adaptiveMemory) {
                const relevantText = fullText.substring(state.memory.lastDistilledLength);
                const lastParagraphBreak = relevantText.lastIndexOf('\n\n');
                
                if (lastParagraphBreak > -1 && relevantText.length - lastParagraphBreak > 100) {
                    textToDistill = relevantText.substring(0, lastParagraphBreak);
                    shouldDistill = true;
                } else if (relevantText.length > 600) {
                    let breakPoint = -1;
                    const punctuationMarks = ['.', '。', '!', '！', '?', '？'];
                    for (let i = Math.min(600, relevantText.length - 1); i >= 0; i--) {
                        if (punctuationMarks.includes(relevantText[i])) {
                            breakPoint = i;
                            break;
                        }
                    }
                    if (breakPoint === -1) breakPoint = 600;
                    textToDistill = relevantText.substring(0, breakPoint + 1);
                    shouldDistill = true;
                }
            } else {
                if (length > state.memory.lastDistilledLength + state.settings.memorySize) {
                    textToDistill = fullText.substring(state.memory.lastDistilledLength, state.memory.lastDistilledLength + state.settings.memorySize);
                    shouldDistill = true;
                }
            }
            
            if (shouldDistill && textToDistill.length > 100) {
                distillMemory(textToDistill);
                state.memory.lastDistilledLength += textToDistill.length;
            }
        }

        async function distillMemory(text) {
            const messages = Prompts.memoryDistillation(text);
            const rawJson = await callZhipuAPI(state.settings.sentenceModel, messages, 0.1);
            
            if (!rawJson) return;

            try {
                const distilledData = JSON.parse(rawJson);
                
                state.memory.permanent.elements.push({
                    title: distilledData.title,
                    summary: distilledData.summary,
                });
                distilledData.keywords.forEach(kw => state.memory.permanent.keywords.add(kw));
                distilledData.patterns.forEach(p => state.memory.permanent.patterns.add(p));
                state.memory.permanent.style = distilledData.style;
                
                if (state.memory.permanent.elements.length >= 3) {
                    const toCombine = state.memory.permanent.elements.splice(0, 2);
                    const combinedSummaries = toCombine.map(e => `${e.title}: ${e.summary}`).join('\n');
                    const combineMessages = Prompts.combineElements(combinedSummaries);
                    const combinedRaw = await callZhipuAPI(state.settings.sentenceModel, combineMessages, 0.1);

                    if (combinedRaw) {
                        try {
                            const combinedData = JSON.parse(combinedRaw);
                            state.memory.permanent.elements.unshift({
                                title: combinedData.title,
                                summary: combinedData.summary,
                            });
                            combinedData.keywords.forEach(kw => state.memory.permanent.keywords.add(kw));
                            combinedData.patterns.forEach(p => state.memory.permanent.patterns.add(p));
                            state.memory.permanent.style = combinedData.style || state.memory.permanent.style;
                        } catch (parseError) {
                            console.error("Failed to parse combined memory JSON:", parseError, "Raw response:", combinedRaw);
                            // 如果解析失败，将原来的元素放回去
                            state.memory.permanent.elements.unshift(...toCombine);
                        }
                    } else {
                        // 如果API调用失败，将原来的元素放回去
                        state.memory.permanent.elements.unshift(...toCombine);
                    }
                }

                if (state.memory.permanent.elements.length > 1) {
                    const combinedSummaries = state.memory.permanent.elements.map(e => `${e.title}: ${e.summary}`).join('\n');
                    state.memory.permanent.summary = await callZhipuAPI(state.settings.sentenceModel, [{role: 'user', content: `将以下摘要整合成一个总概述：\n${combinedSummaries}`}], 0.1);
                } else {
                    state.memory.permanent.summary = distilledData.summary;
                }

                updatePermanentMemoryUI();

            } catch (error) {
                console.error("Failed to parse distilled memory JSON:", error, "Raw response:", rawJson);
            }
        }

        function updateGhostText(before, after) {
            dom.ghostText.textContent = before + state.context.ghostPrediction + after;
            dom.ghostText.scrollTop = dom.editor.scrollTop;
        }

        function updatePinyinCandidatesUI() {
            if (!state.pinyin.isActive || state.pinyin.candidates.length === 0) {
                dom.candidates.style.display = 'none';
                return;
            }
            
            dom.candidates.innerHTML = state.pinyin.candidates.map((word, index) => 
                `<li class="${index === state.pinyin.activeIndex ? 'active' : ''}"><span>${index + 1}</span>${word}</li>`
            ).join('');

            const caretPos = getCaretCoordinates(dom.editor, dom.editor.selectionStart);
            dom.candidates.style.display = 'flex';
            dom.candidates.style.top = `${caretPos.top + caretPos.height}px`;
            dom.candidates.style.left = `${caretPos.left}px`;
        }
        
        function navigatePinyinCandidates(direction) {
            const count = state.pinyin.candidates.length;
            if (count === 0) return;
            state.pinyin.activeIndex = (state.pinyin.activeIndex + direction + count) % count;
            updatePinyinCandidatesUI();
        }

        function updateSettings() {
            state.settings.predictionCount = parseInt(dom.settings.predictionCount.value, 10);
            dom.settings.predictionCountValue.textContent = state.settings.predictionCount;
            state.settings.memorySize = parseInt(dom.settings.memorySize.value, 10);
            dom.settings.memorySizeValue.textContent = state.settings.memorySize;
            state.settings.adaptiveMemory = dom.settings.adaptiveMemoryToggle.checked;
            state.settings.charWordModel = dom.settings.charWordModel.value;
            state.settings.sentenceModel = dom.settings.sentenceModel.value;
            state.settings.apiKey = dom.settings.apiKey.value || DEFAULT_API_KEY;
        }

        function updatePermanentMemoryUI() {
            const mem = state.memory.permanent;
            dom.status.permanentMemoryDisplay.innerHTML = `
<strong>总览:</strong> ${mem.summary}<br>
<strong>风格:</strong> ${mem.style}<br>
<strong>关键词:</strong> ${Array.from(mem.keywords).join(', ') || '无'}<br>
<strong>特征:</strong> ${Array.from(mem.patterns).join(', ') || '无'}
            `;
        }
        
        const debouncedFetchPinyinCandidates = () => {
            clearTimeout(state.debounceTimer);
            state.debounceTimer = setTimeout(fetchPinyinCandidates, 150);
        };
        const debouncedFetchContextualPrediction = () => {
            clearTimeout(state.debounceTimer);
            state.debounceTimer = setTimeout(fetchContextualPrediction, 300);
        };
        
        dom.editor.addEventListener('input', handleInput);
        dom.editor.addEventListener('keydown', handleKeyDown);
        dom.editor.addEventListener('scroll', () => {
            dom.ghostText.scrollTop = dom.editor.scrollTop;
        });

        document.addEventListener('selectionchange', () => {
            if (document.activeElement === dom.editor && !state.pinyin.isActive) {
                debouncedFetchContextualPrediction();
            }
        });

        dom.settings.header.addEventListener('click', () => dom.settings.panel.classList.toggle('collapsed'));
        ['input', 'change'].forEach(evt => {
            dom.settings.predictionCount.addEventListener(evt, updateSettings);
            dom.settings.memorySize.addEventListener(evt, updateSettings);
            dom.settings.charWordModel.addEventListener(evt, updateSettings);
            dom.settings.sentenceModel.addEventListener(evt, updateSettings);
            dom.settings.apiKey.addEventListener(evt, updateSettings);
        });
        dom.settings.adaptiveMemoryToggle.addEventListener('change', updateSettings);
        
        dom.candidates.addEventListener('click', (event) => {
            const li = event.target.closest('li');
            if (li) {
                const index = Array.from(li.parentElement.children).indexOf(li);
                selectPinyinCandidate(index);
            }
        });
        
        function getCaretCoordinates(element, position) {
            const div = document.createElement('div');
            const style = window.getComputedStyle(element);
            
            ['width', 'fontFamily', 'fontSize', 'lineHeight', 'padding'].forEach(prop => {
                div.style[prop] = style[prop];
            });
            div.style.position = 'absolute';
            div.style.visibility = 'hidden';
            div.style.whiteSpace = 'pre-wrap';
            div.style.wordWrap = 'break-word';

            document.body.appendChild(div);
            div.textContent = element.value.substring(0, position);
            
            const span = document.createElement('span');
            span.textContent = element.value.substring(position) || '.';
            div.appendChild(span);
            
            const coords = {
                top: span.offsetTop + element.offsetTop - element.scrollTop,
                left: span.offsetLeft + element.offsetLeft - element.scrollLeft,
                height: span.offsetHeight
            };

            document.body.removeChild(div);
            return coords;
        }

        updateSettings();
    });
    </script>
</body>
</html>